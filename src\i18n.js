// src/i18n.js
import { createI18n } from 'vue-i18n'
import en from './locales/en.json'
import zh from './locales/zh.json'

// 定義支援的語言
const messages = {
  en,
  zh: zh - hk,
}

// 建立 i18n 實例
const i18n = createI18n({
  legacy: false, // ✅ 必須為 false，表示使用 Composition API 模式（Vue 3 推薦）
  locale: 'zh', // ✅ 預設語系
  fallbackLocale: 'zh', // ✅ 如果找不到翻譯，就回退到英文
  messages, // ✅ 所有語系的翻譯內容
})

export default i18n
